import {
  Box,
  GridItem,
  Td,
  Tr,
  Link,
  Button,
  Flex,
  Tag,
} from '@chakra-ui/react';
import React, { useState, useCallback, useRef, useEffect } from 'react';
import { useForm, FormProvider } from 'react-hook-form';
import { Prompt, useHistory } from 'react-router-dom';
import { toast } from 'react-toastify';

import auth from 'modules/auth';

import { formatQueryPagegTable } from 'helpers/format/formatQueryParams';
import isPrenvent from 'helpers/layout/isPrenvent';
import useIsMountedRef from 'helpers/layout/useIsMountedRef';
import useReloadRegistration from 'helpers/layout/useReloadRegistration';
import { shallowEqual } from 'helpers/validation/shallowEqual';

import api, { ResponseApi } from 'services/api';

import {
  GridPaginadaConsulta,
  GridPaginadaRetorno,
} from 'components/Grid/Paginacao';
import { ButtonCadastrarNovo } from 'components/Layout/ButtonCadastrarNovo';
import { ModalConfirmacaoExcluir } from 'components/Modal/ModalConfirmacaoExcluir';
import { ModalConfirmacaoInativar } from 'components/Modal/ModalConfirmacaoInativar';
import { SimpleGridForm } from 'components/update/Form/SimpleGridForm';
import { NumberInput } from 'components/update/Input/NumberInput';
import { SearchInput } from 'components/update/Input/SearchInput';
import { FilterSelect } from 'components/update/Select/FilterSelect';
import { ActionsMenu } from 'components/update/Table/ActionsMenu';
import {
  PagedTable,
  PagedTableForwardRefData,
} from 'components/update/Table/PagedTable';
import { StatusCircle } from 'components/update/Table/StatusCircle';

import ConstanteEnderecoWebservice from 'constants/enderecoWebservice';
import ConstanteFuncionalidades from 'constants/permissoes';
import ConstanteRotas, { SubstituirParametroRota } from 'constants/rotas';

type FormData = {
  ativo: boolean;
  nome: string;
  aliquotaIbs: [{ [key: string]: number }] | null;
};

const formDefaultValues = {
  ativo: true,
  nome: '',
  aliquotaIbs: null,
};

interface Estado {
  id: string;
  nome: string;
  sigla: string;
  aliquotaIbs: number;
}

const Listar = () => {
  const history = useHistory();
  const isMountedRef = useIsMountedRef();

  const [isLoading, setIsLoading] = useState(false);
  const [formIsDirty, setFormIsDirty] = useState(false);
  const [currentFilters, setCurrentFilters] =
    useState<FormData>(formDefaultValues);
  const [estados, setEstados] = useState([] as Array<Estado>);
  const [totalRegistros, setTotalRegistros] = useState(0);

  const pageIsLoaded = useRef(false);
  const childRef = useRef<PagedTableForwardRefData>(null);

  const formMethods = useForm<FormData>({
    defaultValues: formDefaultValues,
  });

  const { handleSubmit, setFocus, formState, getValues } = formMethods;

  const historyReload = useReloadRegistration({ setFormIsDirty });
  isPrenvent(formIsDirty);

  async function alterarIBS() {
    const data = getValues();
    const { aliquotaIbs } = data;
    if (!aliquotaIbs) return false;
    const body = Object.keys(aliquotaIbs).map((key) => {
      return {
        id: key,
        aliquotaIbs: aliquotaIbs[key],
      };
    });
    const response = await api.patch<void, ResponseApi>(
      ConstanteEnderecoWebservice.ESTADO_IBS
    );

    if (response.sucesso) {
      return true;
    }

    if (response.avisos) {
      response.avisos.map((item: string) => toast.warning(item));
    }

    return false;
  }

  const onSubmit = handleSubmit(async () => {
    setIsLoading(true);

    const success = await alterarIBS();

    if (success) {
      toast.success('As alterações foram salvas com sucesso.');

      if (isMountedRef.current) setFocus('nome');

      historyReload(ConstanteRotas.ESTADO);
    }

    setIsLoading(false);
  });

  const paginationHandle = useCallback(
    async (gridPaginadaConsulta: GridPaginadaConsulta) => {
      setIsLoading(true);

      const response = await api.get<
        void,
        ResponseApi<GridPaginadaRetorno<Estado>>
      >(
        formatQueryPagegTable(
          ConstanteEnderecoWebservice.ESTADO,
          gridPaginadaConsulta
        ),
        { params: { ...currentFilters } }
      );

      if (response?.sucesso && isMountedRef.current) {
        setTotalRegistros(response.dados.total);
        setEstados(response.dados.registros);
      }

      if (isMountedRef.current) {
        setIsLoading(false);

        if (!pageIsLoaded.current) {
          pageIsLoaded.current = true;

          setFocus('nome');
        }
      }
    },
    [currentFilters, isMountedRef, setFocus]
  );

  const handleReset = handleSubmit((data) => {
    const filtersIsDirty = !shallowEqual(data, currentFilters || {});

    if (filtersIsDirty) {
      setCurrentFilters(data);
    }
  });

  const headerData = [
    {
      content: 'Sigla',
      key: 'Sigla',
      isOrderable: false,
    },
    {
      content: `Nome`,
      key: 'Nome',
      isOrderable: false,
    },
    {
      content: 'Alíquota IBS',
      key: 'AliquotaIBS',
      isOrderable: false,
    },
  ];

  useEffect(() => {
    setFormIsDirty(formState.isDirty);
  }, [formState.isDirty]);

  return (
    <SimpleGridForm gap={{ base: '10px', sm: '10px', md: 8 }}>
      {formIsDirty && <Prompt when={formIsDirty} message="" />}
      <FormProvider {...formMethods}>
        <GridItem colSpan={{ base: 12, md: 12, lg: 12 }}>
          <SearchInput
            type="search"
            placeholder="Buscar estado por nome"
            onEnterKeyPress={() => handleReset()}
            isDisabled={isLoading}
            id="nome"
            name="nome"
          />
        </GridItem>

        <GridItem mt={['10px', '10px', '-10px']} colSpan={12}>
          <PagedTable
            ref={childRef}
            defaultKeyOrdered="Nome"
            itemsTotalCount={totalRegistros}
            loadColumnsData={paginationHandle}
            isLoading={isLoading}
            tableHeaders={headerData}
            itensPerPage={30}
            pageSizesOptions={[
              { label: '30', value: 30 },
              { label: '50', value: 50 },
              { label: '100', value: 100 },
            ]}
            renderTableRows={estados.map((estado) => (
              <Tr key={estado.id} pl="48px">
                <Td w="128px">
                  <Flex
                    color="gray.700"
                    border="1px solid"
                    borderColor="gray.200"
                    w="48px"
                    justifyContent="center"
                    alignItems="center"
                    height="24px"
                    fontSize="12px"
                    fontWeight="bold"
                    borderRadius="full"
                    textAlign="center"
                  >
                    {estado.sigla}
                  </Flex>
                </Td>
                <Td>{estado.nome}</Td>

                <Td>
                  <NumberInput
                    id={`aliquotaIbs.${estado.id}`}
                    name={`aliquotaIbs.${estado.id}`}
                    scale={2}
                    precision={5}
                    maxW="196px"
                    size="sm"
                  />
                </Td>
              </Tr>
            ))}
          />
        </GridItem>
        <GridItem
          colSpan={{ base: 12, md: 6, lg: 12 }}
          display={{ base: '', sm: '', md: 'flex' }}
          justifyContent={{ base: 'center', sm: 'center' }}
        >
          <Flex gap="24px">
            <Button
              variant="outlineDefault"
              colorScheme="gray"
              w="96px"
              borderRadius="full"
              fontSize="14px"
              isDisabled={isLoading}
              onClick={() => history.push(ConstanteRotas.DASHBOARD)}
            >
              Cancelar
            </Button>
            <Button
              variant="solid"
              colorScheme="secondary"
              w="196px"
              borderRadius="full"
              fontSize="14px"
              isDisabled={isLoading}
              onClick={onSubmit}
            >
              Salvar Alterações
            </Button>
          </Flex>
        </GridItem>
      </FormProvider>
    </SimpleGridForm>
  );
};

export default Listar;
